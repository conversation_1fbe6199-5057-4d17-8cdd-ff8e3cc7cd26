PROC=proc
CC=g++
COPY=cp
RM=rm
LINT=lint

# OpenSSL version detection and configuration
OPENSSL_VERSION := $(shell pkg-config --modversion openssl 2>/dev/null || echo "unknown")
OPENSSL_MAJOR := $(shell echo $(OPENSSL_VERSION) | cut -d. -f1)

# OpenSSL path configuration based on version
ifeq ($(OPENSSL_MAJOR),3)
    # OpenSSL 3.x configuration
    OPENSSL_CFLAGS = -DOPENSSL_API_COMPAT=0x10100000L -DOPENSSL_SUPPRESS_DEPRECATED -DROCKY_LINUX_9
    OPENSSL_INCLUDES = -I/usr/include/openssl
    OPENSSL_LINKFLAGS = -L/usr/lib64
    OPENSSL_LIBS = -lcrypto -lssl
else ifeq ($(OPENSSL_MAJOR),1)
    # OpenSSL 1.x configuration
    OPENSSL_CFLAGS = -DOPENSSL_API_COMPAT=0x10100000L -DOPENSSL_SUPPRESS_DEPRECATED
    OPENSSL_INCLUDES = -I/usr/local/openssl/include
    OPENSSL_LINKFLAGS = -L/usr/local/openssl/lib
    OPENSSL_LIBS = -lcrypto -lssl
else
    # Default/fallback configuration
    OPENSSL_CFLAGS = -DOPENSSL_API_COMPAT=0x10100000L -DOPENSSL_SUPPRESS_DEPRECATED
    OPENSSL_INCLUDES = -I/usr/include/openssl
    OPENSSL_LINKFLAGS = -L/usr/lib64
    OPENSSL_LIBS = -lcrypto -lssl
endif

CFLAGS = -g -Wall -std=gnu++11 $(OPENSSL_CFLAGS)
CURLFLAGS = -g -Wall -D_URL_MODE $(OPENSSL_CFLAGS)
CSKYFLAGS = -g -Wall -D_SKY_MODE $(OPENSSL_CFLAGS)

# Database connection configuration
# Option 1: Use environment variables (recommended for CI/CD)
# Option 2: Use separate config file (recommended for local development)

# Try to include database config file if it exists
-include db_config.mk

# If config file doesn't exist, check environment variables
ifndef SKY_DBSTRING
ifdef DB_CONFIG_FILE
$(error Database config file db_config.mk not found. Copy db_config.mk.template to db_config.mk and set your values)
else
$(error SKY_DBSTRING not set. Either set environment variables or create db_config.mk from template)
endif
endif

ifndef SKY_DBID
$(error SKY_DBID not set. Either set environment variables or create db_config.mk from template)
endif

ifndef SKY_DBPASS
$(error SKY_DBPASS not set. Either set environment variables or create db_config.mk from template)
endif

ORG_D=..
#ORG_D=${HOME}/daemon_sky_ftk
BIN_D=${ORG_D}/bin
OBJ_D=${ORG_D}/obj
LIB_D=${ORG_D}/lib
INC_D=${ORG_D}/inc
SRC_D=${ORG_D}/src
INST_D=/user/neoftk/daemon_sky_ftk/bin

#EXT_LIB=${HOME}/command_kskyb_ftk/obj/sms_ctrlsub++.o
#EXT_INC=${HOME}/command_kskyb_ftk/inc

EXT_LIB=${ORG_D}/../command_kskyb_ftk/obj/sms_ctrlsub++.o
EXT_INC=${ORG_D}/../command_kskyb_ftk/inc

KSLIBRARY_PATH=$(HOME)/library
KSLIBRARY_INC=$(HOME)/library
-
# PC
ORACLE_HOME = /usr/lib/oracle/21/client64

ORALIB1 = ${ORACLE_HOME}/lib
ORALIB2 = ${ORACLE_HOME}/plsql/lib
ORALIB3 = ${ORACLE_HOME}/network/lib
#ORA_INC = ${ORACLE_HOME}/precomp/public
ORA_INC = /usr/include/oracle/21/client64

# Pro*C 설정 - pcscfg.cfg 파일 경로와 관련 설정
PRECOMPPUBLIC = -I${ORACLE_HOME}/lib/precomp/admin

INCLUDE = $(PRECOMPPUBLIC) -I$(INC_D) -I$(LIB_D) -I$(EXT_INC) -I$(ORA_INC) -I/usr/include -I/usr/include/curl $(OPENSSL_INCLUDES)
LINKFLAGS = -L$(ORALIB1) -L$(ORALIB2) -L$(ORALIB3) -L$(ORA_INC) -L/usr/lib64 $(OPENSSL_LINKFLAGS)
ORALIB = -lclntsh
LIBS = -lcurl -lpthread -ldl -lnsl $(OPENSSL_LIBS)

all: telco_sky_new_r

telco_sky_new_r: $(OBJ_D)/telco_sky_new.o $(OBJ_D)/Properties.o $(OBJ_D)/SocketTCP.o $(OBJ_D)/ksbase64.o $(OBJ_D)/PacketCtrlSKY.o $(OBJ_D)/DatabaseORA.o $(OBJ_D)/myException.o $(OBJ_D)/Encrypt.o
	${CC} $(CFLAGS) $^ $(EXT_LIB) -I$(EXT_INC) $(INCLUDE) ${LINKFLAGS} ${SQL_INCLUDE} ${LIBS} ${PROLDLIBS} $(ORALIB) -o $(BIN_D)/telco_sky_new_r_tmp

$(OBJ_D)/telco_sky_new.o: $(SRC_D)/telco_sky_new.cpp
	$(RM) -rf $(OBJ_D)/telco_sky_new.*
	$(COPY) $(SRC_D)/telco_sky_new.cpp $(OBJ_D)/telco_sky_new.pc
	$(PROC) MODE=ORACLE DBMS=V7 UNSAFE_NULL=YES \
		config=${ORACLE_HOME}/lib/precomp/admin/pcscfg.cfg \
		iname=$(OBJ_D)/telco_sky_new.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(SKY_DBID)/$(SKY_DBPASS)@$(SKY_DBSTRING)
	$(CC) $(CFLAGS) -o $(OBJ_D)/telco_sky_new.o -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/telco_sky_new.cpp

$(OBJ_D)/Properties.o: $(LIB_D)/Properties.cpp
	$(RM) -rf $(OBJ_D)/Properties.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/SocketTCP.o: $(LIB_D)/SocketTCP.cpp
	$(RM) -rf $(OBJ_D)/SocketTCP.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/PacketCtrlSKY.o: $(LIB_D)/PacketCtrlSKY.cpp
	$(RM) -rf $(OBJ_D)/PacketCtrlSKY.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) ${LIBS} -lksbase64 -L$(KSLIBRARY_PATH) -c $^

$(OBJ_D)/Encrypt.o: $(LIB_D)/Encrypt.cpp
	$(RM) -rf $@
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} $(OPENSSL_INCLUDES) -I$(EXT_INC) -c $^

$(OBJ_D)/DatabaseORA.o: $(LIB_D)/DatabaseORA.cpp
	$(RM) -rf $(OBJ_D)/DatabaseORA.*
	$(COPY) $(LIB_D)/DatabaseORA.cpp $(OBJ_D)/DatabaseORA.pc
	$(PROC) mode=oracle dbms=v7 unsafe_null=yes char_map=string \
		config=${ORACLE_HOME}/lib/precomp/admin/pcscfg.cfg \
		iname=$(OBJ_D)/DatabaseORA.pc \
		include=$(INC_D) include=$(ORA_INC) \
		include=$(EXT_INC) THREADS=YES CPP_SUFFIX=cpp CODE=CPP PARSE=NONE CTIMEOUT=3 \
		define=__sparc SQLCHECK=SEMANTICS userid=$(SKY_DBID)/$(SKY_DBPASS)@$(SKY_DBSTRING)
	$(CC) $(CSKYFLAGS) -o $(OBJ_D)/DatabaseORA.o -I${INCLUDE} -I$(EXT_INC) -c $(OBJ_D)/DatabaseORA.cpp

$(OBJ_D)/myException.o: $(LIB_D)/myException.cpp
	$(RM) -rf $(OBJ_D)/myException.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^

$(OBJ_D)/ksbase64.o: $(LIB_D)/ksbase64.cpp
	$(RM) -rf $(OBJ_D)/ksbase64.*
	$(CC) -o $@ $(CFLAGS) -I${INCLUDE} -I$(EXT_INC) -c $^
	
clean:
	rm  -rf $(OBJ_D)/*.o tp* $(OBJ_D)/*.lis $(OBJ_D)/*.pc $(OBJ_D)/*.cpp

install:
	mv $(BIN_D)/telco_sky_new_r_tmp $(BIN_D)/telco_sky_ftk
	cp -p $(BIN_D)/telco_sky_ftk $(INST_D)
	rm -f tp*
