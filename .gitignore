# 모든 파일 무시
*

# 확장자가 있는 파일과 디렉터리는 추적
!*.*
!*/

# 확장자가 없지만 추적하고 싶은 파일은 예외로 명시
!README
!LICENSE
!NOTICE
!*akefile

.claude/
tags
*.o
*.a
*.so
cscope.*
obj/
*.log
CVS/
ps_ef_file*
*.bak
*.lis
bak/
back/
*.tar
*.gz
*.zip
*.7z
core.*
bin/
*.swp
*.table
conf.tin
conf.tin.*
*.h.*
*.h_*
*.cpp.*
*.c.*
*.cpp_*
*.txt.*
*.txt_*
makefile.*
bin_*/
*.conf.*
*.conf
*.cfg
*.cert
domain/
Public/
*.sql

# CLion
.idea/
cmake-build-*/
CMakeCache.txt
CMakeFiles/
CMakeScripts/
cmake_install.cmake
compile_commands.json
CTestTestfile.cmake
_deps

# 단, CMake가 생성한 Makefile만 제외
**/cmake-build-*/Makefile
cmake-build-debug/Makefile
cmake-build-release/Makefile

# VS Code
.vscode/
*.code-workspace

# Build directories
build/
debug/
release/
out/

# CMake
CMakeUserPresets.json

# Database configuration files (contains sensitive information)
**/db_config.mk
db_config.mk
**/db_config.cmake
db_config.cmake
