/*
 * DatabaseORA.h
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#ifndef DATABASEORA_H_
#define DATABASEORA_H_

#include <stdafx.h>
#include <string>
#include <set>
#include "senderDbInfo.h"
#include "reportDbInfo.h"

using namespace std;
#include <ml_ctrlsub.h>

extern char _DATALOG[64];

namespace KSKYB
{

// Input parameter class for procCheckMmsLoginExt2 function
class CMmsLoginInput
{
public:
    char cid[64];
    char pwd[64];

    CMmsLoginInput() {
        memset(cid, 0, sizeof(cid));
        memset(pwd, 0, sizeof(pwd));
    }

    CMmsLoginInput(const char* in_cid, const char* in_pwd) {
        strncpy(cid, in_cid ? in_cid : "", sizeof(cid) - 1);
        strncpy(pwd, in_pwd ? in_pwd : "", sizeof(pwd) - 1);
        cid[sizeof(cid) - 1] = '\0';
        pwd[sizeof(pwd) - 1] = '\0';
    }
};

// Output parameter class for procCheckMmsLoginExt2 function
class CMmsLoginOutput
{
public:
    char appname[256];
    char sip[64];
    int pid;
    int job;
    int c_job;
    int prt;
    int cnt;
    int rpt_cnt;
    char server_info[512];
    int rpt_sleep_cnt;
    char sender_proc[256];
    char report_proc[256];
    char senderdb_info[512];
    char reportdb_info[512];
    char logfile_info[512];
    char etc[512];
    int rst;
    char rstmsg[512];

    CMmsLoginOutput() {
        memset(appname, 0, sizeof(appname));
        memset(sip, 0, sizeof(sip));
        pid = 0;
        job = 0;
        c_job = 0;
        prt = 0;
        cnt = 0;
        rpt_cnt = 0;
        memset(server_info, 0, sizeof(server_info));
        rpt_sleep_cnt = 0;
        memset(sender_proc, 0, sizeof(sender_proc));
        memset(report_proc, 0, sizeof(report_proc));
        memset(senderdb_info, 0, sizeof(senderdb_info));
        memset(reportdb_info, 0, sizeof(reportdb_info));
        memset(logfile_info, 0, sizeof(logfile_info));
        memset(etc, 0, sizeof(etc));
        rst = 0;
        memset(rstmsg, 0, sizeof(rstmsg));
    }
};

class CLimitDefInput {
public:
	int pid;
	CLimitDefInput() { pid = 0; }
	CLimitDefInput(int in_pid) { pid = in_pid; }
};

class CLimitDefOutput
{
public:
	char limit_type[256];
	int day_warn_cnt;
	int day_limit_cnt;
	int mon_warn_cnt;
	int mon_limit_cnt;
	char limit_flag[256];
	int day_acc_cnt;
	int mon_acc_cnt;
	int rst;
	char rstmsg[512];

	CLimitDefOutput() {
		memset(limit_type, 0, sizeof(limit_type));
		day_warn_cnt = 0;
		day_limit_cnt = 0;
		mon_warn_cnt = 0;
		mon_limit_cnt = 0;
		memset(limit_flag, 0, sizeof(limit_flag));
		day_acc_cnt = 0;
		mon_acc_cnt = 0;
		rst = 0;
		memset(rstmsg, 0, sizeof(rstmsg));
	}
};	

class CDatabaseORA
{
public:
	CDatabaseORA() {};
	virtual ~CDatabaseORA() {};

	int connectToOracle(char*, char*);
	int closeFromOracle();
	int commitOracle();
	int rollbackOracle();
	int getCTNID();
	int setMMSCTNTBL(CSenderDbMMSCTNTBL &ctn_data);
	int setMMSTBL(CSenderDbMMSTBL &mms_data);
	int setMMSMSG(CSenderDbMMSMSG &que_data);
	int getReportDB(CReportDbInfo &rpt_data);
	int setRPTTBL(CSenderDbMMSRPTTBL &rpt_data);
	int selectTblCallback(set<string>& set_callback_list, int _pid);
	int selectAllowDialCode(set<string>& set_dialcode_list, char *_dial_code_type);
	char* trim(char* szOrg, int leng);
	int setMMSMSG_TALK(CSenderDbMMSMSG_TALK &que_data);
	int setMMSMSG_TALKBt(CSenderDbMMSMSG_TALK &que_data);
	//int setMMSMSG_FTALK(CSenderDbMMSMSG_FTALK &que_data);
	//int setMMSMSG_FTALK_V2(CSenderDbMMSMSG_FTALK &que_data);
	//added due to button API change
	int setMMSMSG_FTK_V3(CSenderDbMMSMSG_FTALK &que_data);
	int setMMSMSG_FTKUP(CSenderDbMMSMSG_FTALKUP &que_data);
	//int getMMSID();
	long long getMMSID();
	
	//20180822 prevent report omission
	int setSendReportData(CSenderDbMMSRPTQUE &rpt_data);
	// 20170621 MMSID SEQ USE
	int selectSEQ();

	// logonDB dedicated methods
	int procCheckMmsLoginExt2(const CMmsLoginInput& input, CMmsLoginOutput& output);

	// int procGetLimitDef(int in_pid, char* ot_limit_type, int* ot_day_warn_cnt,
	//                    int* ot_day_limit_cnt, int* ot_mon_warn_cnt, int* ot_mon_limit_cnt,
	//                    char* ot_limit_flag, int* ot_day_acc_cnt, int* ot_mon_acc_cnt,
	//                    int* ot_rst, char* ot_rstmsg);
	int procGetLimitDef(const CLimitDefInput& input, CLimitDefOutput& output);	
	
private:
	inline string trimR(const string& str)
	{
		string::size_type n = str.find_last_not_of(" \t\v\n");
		return n == string::npos ? str : str.substr(0, n + 1);
	}
};

}

#endif /* DATABASEORA_H_ */
